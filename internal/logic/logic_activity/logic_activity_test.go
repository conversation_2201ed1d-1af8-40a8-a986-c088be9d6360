package logic_activity

import (
	"activitysrv/internal/model"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetAvailableStagesForClaim(t *testing.T) {
	logic := NewActivityLogic()

	// 创建测试用户数据
	userData := model.NewUserActivityData()

	// 设置一些指标数据
	userData.Metrics[model.MetricTypeFishWeight] = 15000       // 累计鱼重量15000
	userData.Metrics[model.MetricTypeFishCount] = 50           // 累计鱼数量50
	userData.MetricsMax[model.MetricTypeMaxSingleWeight] = 600 // 单次最大重量600

	// 测试获取可领取阶段
	availableStages, err := logic.getAvailableStagesForClaim(userData)

	assert.NoError(t, err, "获取可领取阶段应该成功")
	assert.NotEmpty(t, availableStages, "应该有可领取的阶段")

	// 根据硬编码的阶段配置，应该可以领取阶段1、2、3
	expectedStages := []int32{1, 2, 3}
	assert.ElementsMatch(t, expectedStages, availableStages, "可领取阶段应该匹配")
}

func TestGetAvailableStagesForClaim_WithClaimedStages(t *testing.T) {
	logic := NewActivityLogic()

	// 创建测试用户数据
	userData := model.NewUserActivityData()

	// 设置指标数据
	userData.Metrics[model.MetricTypeFishWeight] = 15000
	userData.Metrics[model.MetricTypeFishCount] = 50
	userData.MetricsMax[model.MetricTypeMaxSingleWeight] = 600

	// 设置已领取的阶段
	userData.ClaimStage(1, `[{"item_id":1001,"count":10}]`)
	userData.ClaimStage(2, `[{"item_id":1001,"count":50}]`)

	// 测试获取可领取阶段
	availableStages, err := logic.getAvailableStagesForClaim(userData)

	assert.NoError(t, err, "获取可领取阶段应该成功")

	// 应该只能领取阶段3（阶段1、2已领取）
	expectedStages := []int32{3}
	assert.ElementsMatch(t, expectedStages, availableStages, "可领取阶段应该匹配")
}

func TestCheckStageCondition(t *testing.T) {
	// 创建测试用户数据
	userData := model.NewUserActivityData()
	userData.Metrics[model.MetricTypeFishWeight] = 5000
	userData.Metrics[model.MetricTypeFishCount] = 30
	userData.MetricsMax[model.MetricTypeMaxSingleWeight] = 400

	// 测试阶段1条件（需要鱼重量1000）
	stage1 := &StageConfig{
		StageId: 1,
		Conditions: map[int32]int64{
			model.MetricTypeFishWeight: 1000,
		},
	}
	assert.True(t, checkStageCondition(userData, stage1), "应该满足阶段1条件")

	// 测试阶段3条件（需要鱼重量10000和单次最大重量500）
	stage3 := &StageConfig{
		StageId: 3,
		Conditions: map[int32]int64{
			model.MetricTypeFishWeight:      10000,
			model.MetricTypeMaxSingleWeight: 500,
		},
	}
	assert.False(t, checkStageCondition(userData, stage3), "不应该满足阶段3条件")
}

func TestBuildRewardConfig(t *testing.T) {
	stageCfg := &StageConfig{
		StageId: 1,
		Rewards: []RewardItem{
			{ItemId: 1001, Count: 10},
			{ItemId: 2001, Count: 1},
		},
	}

	rewardConfig := buildRewardConfig(stageCfg)
	assert.NotEmpty(t, rewardConfig, "奖励配置不应为空")
	assert.Contains(t, rewardConfig, "1001", "奖励配置应该包含物品ID")
	assert.Contains(t, rewardConfig, "10", "奖励配置应该包含数量")
}
