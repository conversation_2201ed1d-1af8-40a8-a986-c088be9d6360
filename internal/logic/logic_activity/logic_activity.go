package logic_activity

import (
	"activitysrv/internal/dao/dao_activity"
	"activitysrv/internal/model"
	"context"
	"encoding/json"
	"fmt"
	activityPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/activity"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"time"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
)

// StageConfig 阶段配置
type StageConfig struct {
	StageId    int32           `json:"stage_id"`   // 阶段ID
	Conditions map[int32]int64 `json:"conditions"` // 完成条件，key为指标类型，value为需要达到的值
	Rewards    []RewardItem    `json:"rewards"`    // 奖励列表
}

// RewardItem 奖励物品
type RewardItem struct {
	ItemId int32 `json:"item_id"` // 物品ID
	Count  int32 `json:"count"`   // 数量
}

// ActivityLogic 爆护之路业务逻辑
type ActivityLogic struct {
}

// NewActivityLogic 创建爆护之路业务逻辑实例
func NewActivityLogic() *ActivityLogic {
	return &ActivityLogic{}
}

// HandleEvent 处理事件 - 实现ActivityHandler接口
func (logic *ActivityLogic) HandleEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon) error {
	entry := logx.NewLogEntry(ctx)

	// 1. 获取所有活动配置
	allActivities := cmodel.GetAllActivity(consul_config.WithGrpcCtx(ctx))
	if allActivities == nil {
		entry.Debugf("活动配置不存在")
		return nil
	}

	// 2. 筛选当前开启且支持该事件类型的活动
	activeActivities := filterActiveActivitiesForEvent(allActivities, event)
	if len(activeActivities) == 0 {
		entry.Debugf("没有活跃的活动需要处理此事件: eventType=%v", event.EventType)
		return nil
	}

	for _, activityCfg := range activeActivities {
		err := logic.processActivityEvent(ctx, playerId, event, activityCfg, entry)
		if err != nil {
			entry.Errorf("处理活动事件失败: activityId=%d, err=%v", activityCfg.Id, err)
		}
	}

	return nil
}

// GetProgress 获取活动进度 - 实现ActivityHandler接口
func (logic *ActivityLogic) GetProgress(ctx context.Context, playerId uint64, req *activityPB.GetActivityProgressReq) (*activityPB.GetActivityProgressRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 转换活动ID类型
	activityId := int64(req.GetActivityId())

	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("爆护之路活动配置不存在")
	}

	// 获取当前周期
	currentCycle, err := dao_activity.CheckAndCreateCycleIfNeeded(ctx, activityId, activityCfg.CycleDays)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}

	// 获取当前周期的用户数据
	currentUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, currentCycle.CycleId)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期用户数据失败: %w", err)
	}

	// 构建进度数据
	progress := &model.ExplosiveProtectionProgress{
		ActivityId:     activityId,
		CurrentCycleId: currentCycle.CycleId,
		CycleEndTime:   currentCycle.EndTime,
		Metrics:        currentUserData.Metrics,
		ClaimedRecords: currentUserData.GetClaimedStagesList(),
	}

	// 获取上个周期数据（如果存在）
	if currentCycle.CycleId > 1 {
		previousCycleId := currentCycle.CycleId - 1
		previousUserData, err := dao_activity.GetUserData(ctx, activityId, playerId, previousCycleId)
		if err != nil {
			entry.Warnf("获取上个周期用户数据失败: %v", err)
		} else {
			progress.PreviousCycle = &model.ExplosiveProtectionProgress{
				ActivityId:     activityId,
				CurrentCycleId: previousCycleId,
				Metrics:        previousUserData.Metrics,
				ClaimedRecords: previousUserData.GetClaimedStagesList(),
			}
		}
	}

	entry.Debugf("成功获取活动进度: activityId=%d, playerId=%d, currentCycleId=%d",
		activityId, playerId, currentCycle.CycleId)

	// 由于protobuf结构体字段不明确，暂时返回简化的响应
	// TODO: 根据实际protobuf定义调整字段映射
	return &activityPB.GetActivityProgressRsp{
		// 这里需要根据实际的protobuf定义来设置字段
	}, nil
}

// ClaimReward 领取奖励 - 实现ActivityHandler接口
func (logic *ActivityLogic) ClaimReward(ctx context.Context, playerId uint64, req *activityPB.ClaimActivityRewardReq) (*activityPB.ClaimActivityRewardRsp, error) {
	entry := logx.NewLogEntry(ctx)

	// 转换活动ID类型
	activityId := int64(req.GetActivityId())

	// 获取活动配置
	activityCfg := cmodel.GetActivity(activityId, consul_config.WithGrpcCtx(ctx))
	if activityCfg == nil {
		return nil, fmt.Errorf("爆护之路活动配置不存在")
	}

	// 校验活动时间（奖励过期策略：仅保留上一周期）
	currentCycle, err := dao_activity.GetCurrentCycle(ctx, activityId)
	if err != nil {
		return nil, fmt.Errorf("获取当前周期失败: %w", err)
	}
	if currentCycle == nil {
		return nil, fmt.Errorf("当前没有活跃的活动周期")
	}

	// 检查周期是否有效（当前周期或上一周期）
	if req.GetCycleId() != currentCycle.CycleId && req.GetCycleId() != currentCycle.CycleId-1 {
		return nil, fmt.Errorf("周期 %d 的奖励已过期，仅支持当前周期和上一周期的奖励领取", req.GetCycleId())
	}

	// 获取用户数据
	userData, err := dao_activity.GetUserData(ctx, activityId, playerId, req.GetCycleId())
	if err != nil {
		return nil, fmt.Errorf("获取用户数据失败: %w", err)
	}

	// 查询玩家当前可以领取的所有阶段
	availableStages, err := logic.getAvailableStagesForClaim(userData)
	if err != nil {
		return nil, fmt.Errorf("获取可领取阶段失败: %w", err)
	}

	if len(availableStages) == 0 {
		return nil, fmt.Errorf("当前没有可领取的奖励阶段")
	}

	// 领取所有可领取的阶段奖励
	var claimedStageIds []int32
	for _, stageId := range availableStages {
		err := logic.claimSingleStage(ctx, activityId, playerId, req.GetCycleId(), stageId)
		if err != nil {
			entry.Errorf("领取阶段 %d 奖励失败: %v", stageId, err)
			continue // 继续领取其他阶段
		}
		claimedStageIds = append(claimedStageIds, stageId)
	}

	if len(claimedStageIds) == 0 {
		return nil, fmt.Errorf("所有阶段奖励领取失败")
	}

	entry.Infof("成功领取爆护之路奖励: activityId=%d, playerId=%d, cycleId=%d, stages=%v",
		activityId, playerId, req.GetCycleId(), claimedStageIds)

	// 由于protobuf结构体字段不明确，暂时返回简化的响应
	// TODO: 根据实际protobuf定义调整字段映射
	return &activityPB.ClaimActivityRewardRsp{
		// 这里需要根据实际的protobuf定义来设置字段
	}, nil
}

// getAvailableStagesForClaim 获取玩家当前可以领取的所有阶段
func (logic *ActivityLogic) getAvailableStagesForClaim(userData *model.UserActivityData) ([]int32, error) {
	// 获取所有阶段配置
	allStageConfigs, err := getAllStageConfigs()
	if err != nil {
		return nil, fmt.Errorf("获取阶段配置失败: %w", err)
	}

	var availableStages []int32
	for _, stageCfg := range allStageConfigs {
		// 检查是否已经领取过
		if userData.IsStageClaimedStage(stageCfg.StageId) {
			continue
		}

		// 检查是否满足领取条件
		if checkStageCondition(userData, stageCfg) {
			availableStages = append(availableStages, stageCfg.StageId)
		}
	}

	return availableStages, nil
}

// claimSingleStage 领取单个阶段奖励
func (logic *ActivityLogic) claimSingleStage(ctx context.Context, activityId int64, playerId uint64, cycleId int32, stageId int32) error {
	// 获取阶段配置
	stageCfg, err := getStageConfig(stageId)
	if err != nil {
		return fmt.Errorf("获取阶段配置失败: %w", err)
	}

	// 原子性领取奖励
	rewardConfig := buildRewardConfig(stageCfg)
	err = dao_activity.ClaimReward(ctx, activityId, playerId, cycleId, stageId, rewardConfig)
	if err != nil {
		return fmt.Errorf("领取奖励失败: %w", err)
	}

	// TODO: 调用大厅服发奖接口
	// err = logic.sendRewardToHall(ctx, playerId, stageCfg.Rewards)
	// if err != nil {
	//     logx.NewLogEntry(ctx).Errorf("发放奖励失败: %v", err)
	//     // 记录失败日志用于后续补偿，但不回滚已领取状态
	// }

	return nil
}

// getStageConfig 获取阶段配置
func getStageConfig(stageId int32) (*StageConfig, error) {
	// 这里应该从配置中心或数据库获取阶段配置
	// 暂时使用硬编码的配置作为示例
	stageConfigs := getHardcodedStageConfigs()

	for _, config := range stageConfigs {
		if config.StageId == stageId {
			return config, nil
		}
	}

	return nil, fmt.Errorf("阶段配置不存在: stageId=%d", stageId)
}

// getAllStageConfigs 获取所有阶段配置
func getAllStageConfigs() ([]*StageConfig, error) {
	// 这里应该从配置中心或数据库获取所有阶段配置
	// 暂时使用硬编码的配置作为示例
	return getHardcodedStageConfigs(), nil
}

// getHardcodedStageConfigs 获取硬编码的阶段配置（示例）
func getHardcodedStageConfigs() []*StageConfig {
	return []*StageConfig{
		{
			StageId: 1,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight: 1000, // 累计鱼重量达到1000
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 10}, // 金币 x10
			},
		},
		{
			StageId: 2,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight: 5000, // 累计鱼重量达到5000
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 50}, // 金币 x50
			},
		},
		{
			StageId: 3,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight:      10000, // 累计鱼重量达到10000
				model.MetricTypeMaxSingleWeight: 500,   // 单次最大重量达到500
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 100}, // 金币 x100
				{ItemId: 2001, Count: 1},   // 特殊道具 x1
			},
		},
		{
			StageId: 4,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight: 20000, // 累计鱼重量达到20000
				model.MetricTypeFishCount:  100,   // 累计鱼数量达到100
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 200}, // 金币 x200
				{ItemId: 2002, Count: 1},   // 高级道具 x1
			},
		},
		{
			StageId: 5,
			Conditions: map[int32]int64{
				model.MetricTypeFishWeight:      50000, // 累计鱼重量达到50000
				model.MetricTypeMaxSingleWeight: 1000,  // 单次最大重量达到1000
			},
			Rewards: []RewardItem{
				{ItemId: 1001, Count: 500}, // 金币 x500
				{ItemId: 3001, Count: 1},   // 稀有道具 x1
			},
		},
	}
}

// extractMetricsFromEvent 从事件中提取指标数据
func extractMetricsFromEvent(event *commonPB.EventCommon, cfg *cmodel.Activity) (map[int32]int64, error) {
	metrics := make(map[int32]int64)

	if event.EventType == commonPB.EVENT_TYPE_ET_FISH_GET {
		// 提取鱼重量
		if weight, exists := event.IntData[int32(commonPB.EVENT_INT_KEY_EIK_FISH_WEIGHT)]; exists {
			metrics[model.MetricTypeFishWeight] = weight
			metrics[model.MetricTypeMaxSingleWeight] = weight // 用于最大值比较
		}

		// 提取鱼数量（默认为1）
		metrics[model.MetricTypeFishCount] = 1 // 默认捕获1条鱼
	}

	return metrics, nil
}

// checkStageCondition 检查阶段完成条件
func checkStageCondition(userData *model.UserActivityData, stageCfg *StageConfig) bool {
	for metricType, requiredValue := range stageCfg.Conditions {
		currentValue := userData.GetMetricValue(metricType)
		if currentValue < requiredValue {
			return false
		}
	}
	return true
}

// buildRewardConfig 构建奖励配置
func buildRewardConfig(stageCfg *StageConfig) string {
	rewardData, _ := json.Marshal(stageCfg.Rewards)
	return string(rewardData)
}

// isActivityActive 检查活动是否活跃
func isActivityActive(cfg *cmodel.Activity) bool {
	// 检查活动时间
	now := time.Now().Unix()
	if now < cfg.OpenAt || (cfg.CloseAt > 0 && now > cfg.CloseAt) {
		return false
	}

	return true
}

// hasActivityMetrics 检查事件是否包含活动相关指标
func hasActivityMetrics(event *commonPB.EventCommon, cfg *cmodel.Activity) bool {
	if _, exists := event.IntData[cfg.Target]; exists {
		return true
	}
	return false
}

// checkAvailableRewards 检查是否有可领取的奖励
func checkAvailableRewards(metrics map[int32]int64, claimedRecords []int32, stageConfigs []*StageConfig) bool {
	// 构建已领取阶段的map
	claimedMap := make(map[int32]bool)
	for _, stageId := range claimedRecords {
		claimedMap[stageId] = true
	}

	// 检查每个阶段
	for _, stageCfg := range stageConfigs {
		// 如果已经领取过，跳过
		if claimedMap[stageCfg.StageId] {
			continue
		}

		// 检查是否满足条件
		canClaim := true
		for metricType, requiredValue := range stageCfg.Conditions {
			currentValue := metrics[metricType]
			if currentValue < requiredValue {
				canClaim = false
				break
			}
		}

		if canClaim {
			return true
		}
	}

	return false
}

// filterActiveActivitiesForEvent 筛选当前开启且支持该事件类型的活动
func filterActiveActivitiesForEvent(allActivities map[int64]*cmodel.Activity, event *commonPB.EventCommon) []*cmodel.Activity {
	var activeActivities []*cmodel.Activity

	for _, activityCfg := range allActivities {
		// 检查活动是否活跃
		if !isActivityActive(activityCfg) {
			continue
		}

		// 检查活动指标是否包含在事件中
		if !hasActivityMetrics(event, activityCfg) {
			continue
		}

		activeActivities = append(activeActivities, activityCfg)
	}

	return activeActivities
}

// processActivityEvent 处理单个活动的事件
func (logic *ActivityLogic) processActivityEvent(ctx context.Context, playerId uint64, event *commonPB.EventCommon, activityCfg *cmodel.Activity, entry interface{}) error {
	// 1. 周期管理：检查当前周期是否已结束，如果结束则自动创建新周期
	currentCycle, err := dao_activity.CheckAndCreateCycleIfNeeded(ctx, activityCfg.Id, activityCfg.CycleDays)
	if err != nil {
		return fmt.Errorf("检查或创建活动周期失败: activityId=%d, %w", activityCfg.Id, err)
	}

	// 2. 指标更新：提取事件中的指标数据并更新
	metricUpdates, err := extractMetricsFromEvent(event, activityCfg)
	if err != nil {
		return fmt.Errorf("提取事件指标失败: activityId=%d, %w", activityCfg.Id, err)
	}

	if len(metricUpdates) == 0 {
		if logger, ok := entry.(interface{ Debugf(string, ...interface{}) }); ok {
			logger.Debugf("事件中没有需要更新的指标: activityId=%d", activityCfg.Id)
		}
		return nil
	}

	// 3. 原子性更新玩家指标数据
	err = dao_activity.UpdateUserMetrics(ctx, activityCfg.Id, playerId,
		currentCycle.CycleId, metricUpdates, model.MetricOperationAdd)
	if err != nil {
		return fmt.Errorf("更新玩家指标失败: activityId=%d, %w", activityCfg.Id, err)
	}

	if logger, ok := entry.(interface{ Debugf(string, ...interface{}) }); ok {
		logger.Debugf("成功处理活动事件: activityId=%d, playerId=%d, cycleId=%d, metrics=%v",
			activityCfg.Id, playerId, currentCycle.CycleId, metricUpdates)
	}

	return nil
}
